# PowerShell script to activate virtual environment
# Usage: .\activate_env.ps1

Write-Host "=== 啟動虛擬環境 ===" -ForegroundColor Green

# 檢查虛擬環境是否存在
$venvPath = ".\venv_win_3_11_9"
$activateScript = "$venvPath\Scripts\Activate.ps1"

if (Test-Path $activateScript) {
    Write-Host "找到虛擬環境: $venvPath" -ForegroundColor Yellow
    
    # 檢查執行策略
    $policy = Get-ExecutionPolicy
    if ($policy -eq "Restricted") {
        Write-Host "警告: PowerShell 執行策略受限制" -ForegroundColor Red
        Write-Host "請先執行: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Yellow
        Write-Host "或暫時繞過: PowerShell -ExecutionPolicy Bypass -File activate_env.ps1" -ForegroundColor Yellow
    }
    
    try {
        # 激活虛擬環境
        & $activateScript
        
        # 顯示 Python 版本和虛擬環境資訊
        Write-Host ""
        Write-Host "=== 虛擬環境已啟動 ===" -ForegroundColor Green
        Write-Host "Python 版本:" -ForegroundColor Cyan
        python --version
        Write-Host "虛擬環境路徑:" -ForegroundColor Cyan
        Write-Host $env:VIRTUAL_ENV
        Write-Host ""
        Write-Host "可用的快捷命令:" -ForegroundColor Yellow
        Write-Host "  python -m pip list          # 查看已安裝套件"
        Write-Host "  python -m pip install -r requirements.txt  # 安裝依賴"
        Write-Host "  python start_integrated_services.py        # 啟動服務"
        Write-Host "  deactivate                   # 退出虛擬環境"
        Write-Host ""
        
        # 檢查是否有 requirements.txt
        if (Test-Path "requirements.txt") {
            Write-Host "發現 requirements.txt 檔案" -ForegroundColor Cyan
            Write-Host "如需安裝依賴，請執行: python -m pip install -r requirements.txt" -ForegroundColor Yellow
        }
        
        # 顯示當前目錄的主要檔案
        Write-Host "項目主要檔案:" -ForegroundColor Cyan
        Get-ChildItem -Path . -Filter "*.py" | Select-Object -First 5 | ForEach-Object {
            Write-Host "  - $($_.Name)" -ForegroundColor Gray
        }
        if ((Get-ChildItem -Path . -Filter "*.py").Count -gt 5) {
            Write-Host "  ... 以及更多 Python 檔案" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "激活虛擬環境時發生錯誤: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "請檢查虛擬環境是否正確建立" -ForegroundColor Yellow
    }
} else {
    Write-Host "虛擬環境不存在: $venvPath" -ForegroundColor Red
    Write-Host "請先建立虛擬環境:" -ForegroundColor Yellow
    Write-Host "  python -m venv venv_win_3_11_9" -ForegroundColor Gray
    Write-Host "然後再執行此腳本" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== 腳本執行完成 ===" -ForegroundColor Green